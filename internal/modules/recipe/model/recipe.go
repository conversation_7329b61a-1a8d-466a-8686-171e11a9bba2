package model

import "time"

type Recipe struct {
	ID          string
	Name        string
	Code        string
	ProductIDs  []string
	Components  []RecipeComponent
	CreatedAt   *time.Time
	UpdatedAt   *time.Time
	DeletedAt   *time.Time
}

type RecipeComponent struct {
	ProductID         string
	ProductCategoryID string // Main category (parent_id is null)
	Quantity          float64
}

type RecipeCreate struct {
	Name       string
	Code       string
	ProductIDs []string
	Components []RecipeComponent
}

type RecipeUpdate struct {
	ID         string
	Name       string
	Code       string
	ProductIDs []string
	Components []RecipeComponent
}
