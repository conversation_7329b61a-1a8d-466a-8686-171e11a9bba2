package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (r *recipePostgreRepo) GetAll(ctx context.Context) ([]model.Recipe, error) {
	var recipes []model.Recipe

	err := pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT
				r.id, r.name, r.code, r.created_at, r.updated_at, r.deleted_at,
				COALESCE(ARRAY_AGG(DISTINCT rp.product_id ORDER BY rp.created_at ASC) FILTER (WHERE rp.product_id IS NOT NULL), '{}') as product_ids,
				COALESCE(ARRAY_AGG(rc.product_id ORDER BY rc.created_at ASC) FILTER (WHERE rc.product_id IS NOT NULL), '{}') as component_product_ids,
				COALESCE(ARRAY_AGG(
					COALESCE(
						(SELECT c.id 
						 FROM products p 
						 JOIN product_categories pc ON p.id = pc.product_id 
						 JOIN categories c ON pc.category_id = c.id 
						 WHERE p.id = rc.product_id AND c.category_id IS NULL 
						 LIMIT 1), 
						''
					) ORDER BY rc.created_at ASC
				) FILTER (WHERE rc.product_id IS NOT NULL), '{}') as component_category_ids,
				COALESCE(ARRAY_AGG(rc.quantity ORDER BY rc.created_at ASC) FILTER (WHERE rc.quantity IS NOT NULL), '{}') as component_quantities
			FROM recipes r
			LEFT JOIN recipe_products rp ON r.id = rp.recipe_id
			LEFT JOIN recipe_components rc ON r.id = rc.recipe_id
			WHERE r.deleted_at IS NULL
			GROUP BY r.id, r.name, r.code, r.created_at, r.updated_at, r.deleted_at
			ORDER BY r.created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get all recipes", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var recipe model.Recipe
			var productIDs []string
			var componentProductIDs []string
			var componentCategoryIDs []string
			var componentQuantities []float64

			err := rows.Scan(
				&recipe.ID,
				&recipe.Name,
				&recipe.Code,
				&recipe.CreatedAt,
				&recipe.UpdatedAt,
				&recipe.DeletedAt,
				&productIDs,
				&componentProductIDs,
				&componentCategoryIDs,
				&componentQuantities,
			)

			if err != nil {
				return utils.InternalErrorf("failed to scan recipe", err, nil)
			}

			// Convert arrays to slices
			recipe.ProductIDs = []string(productIDs)

			// Build components array
			recipe.Components = make([]model.RecipeComponent, len(componentProductIDs))
			for i, productID := range componentProductIDs {
				recipe.Components[i] = model.RecipeComponent{
					ProductID:         productID,
					ProductCategoryID: componentCategoryIDs[i],
					Quantity:          componentQuantities[i],
				}
			}

			recipes = append(recipes, recipe)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("failed to iterate recipes", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return recipes, nil
}
